@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

:root {
  --foreground-rgb: 30, 41, 59; /* slate-800 from logo text */
  --background-rgb: 255, 255, 255;
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  overflow-y: scroll;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Full-page section snap scrolling - Fixed implementation */
.snap-section {
  scroll-snap-align: start;
  scroll-snap-stop: always;
  min-height: 100vh;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  width: 100%;
}

@layer components {
  /* Modern primary buttons with teal-green gradient */
  .btn-primary {
    @apply bg-gradient-to-r from-accent to-accent-dark
           hover:from-accent-dark hover:to-emerald-600
           text-white font-semibold py-4 px-8 rounded-xl
           transition-all duration-300 shadow-lg hover:shadow-xl
           transform hover:-translate-y-1 active:translate-y-0
           relative overflow-hidden;
  }

  .btn-primary::before {
    @apply content-[''] absolute inset-0 bg-gradient-to-r from-accent-light to-accent
           opacity-0 transition-opacity duration-300;
  }

  .btn-primary:hover::before {
    @apply opacity-100;
  }

  /* Secondary buttons with modern tech styling */
  .btn-secondary {
    @apply bg-white border-2 border-slate-200
           hover:border-accent hover:bg-gradient-to-r hover:from-accent/5 hover:to-tech/5
           text-slate-700 hover:text-accent font-semibold py-4 px-8 rounded-xl
           transition-all duration-300 shadow-sm hover:shadow-lg
           transform hover:-translate-y-0.5;
  }

  /* Modern navigation links */
  .nav-link {
    @apply text-slate-600 hover:text-accent transition-colors duration-300
           font-medium relative;
  }

  .nav-link::after {
    @apply content-[''] absolute bottom-0 left-0 w-0 h-0.5 bg-accent
           transition-all duration-300;
  }

  .nav-link:hover::after {
    @apply w-full;
  }

  /* Modern tech cards with glassmorphism effect */
  .card {
    @apply bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-sm
           border border-slate-100/50 hover:border-accent/30
           hover:shadow-2xl hover:shadow-accent/10 transition-all duration-500
           transform hover:-translate-y-2 relative overflow-hidden;
  }

  .card::before {
    @apply content-[''] absolute inset-0 bg-gradient-to-br from-accent/5 to-tech/5
           opacity-0 transition-opacity duration-500;
  }

  .card:hover::before {
    @apply opacity-100;
  }

  /* Modern tech section headings */
  .section-heading {
    @apply text-4xl font-display font-bold text-slate-900 sm:text-5xl lg:text-6xl
           leading-tight tracking-tight bg-gradient-to-r from-slate-900 to-slate-700
           bg-clip-text text-transparent;
  }

  /* Enhanced section subheadings with modern styling */
  .section-subheading {
    @apply text-xl text-slate-600 leading-relaxed sm:text-2xl
           max-w-4xl mx-auto font-medium;
  }

  /* Modern accent elements */
  .accent {
    @apply text-accent font-semibold bg-gradient-to-r from-accent to-accent-dark
           bg-clip-text text-transparent;
  }

  /* Container width for consistent layout */
  .container-width {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Enhanced modern form styles */
  .form-input {
    @apply mt-2 block w-full px-6 py-4
           bg-white border-2 border-slate-200
           rounded-xl shadow-sm
           placeholder:text-slate-400
           focus:outline-none focus:ring-4 focus:ring-accent/10
           focus:border-accent transition-all duration-300
           hover:border-slate-300;
  }

  .form-label {
    @apply block text-sm font-semibold text-slate-700 mb-2;
  }

  /* Form group wrapper for consistent spacing */
  .form-group {
    @apply space-y-2;
  }

  /* Modern tech feature icon containers */
  .feature-icon {
    @apply w-16 h-16 bg-gradient-to-br from-accent/10 via-tech/5 to-accent/5
           rounded-2xl flex items-center justify-center
           transition-all duration-500 relative overflow-hidden
           shadow-lg shadow-accent/10;
  }

  .feature-icon::before {
    @apply content-[''] absolute inset-0 bg-gradient-to-br from-accent/20 to-tech/20
           opacity-0 transition-opacity duration-500;
  }

  .feature-icon:hover::before {
    @apply opacity-100;
  }

  /* Modern page indicator with tooltips */
  .page-indicator {
    @apply fixed right-8 top-1/2 transform -translate-y-1/2 z-40
           hidden lg:flex flex-col space-y-4;
  }

  .page-dot {
    @apply w-4 h-4 rounded-full bg-slate-300 cursor-pointer
           transition-all duration-300 hover:bg-accent/70 hover:scale-125
           relative group;
  }

  .page-dot.active {
    @apply bg-accent w-5 h-5 shadow-lg shadow-accent/30;
  }

  /* Tooltip styling */
  .page-dot::after {
    @apply content-[attr(data-tooltip)] absolute right-8 top-1/2 transform -translate-y-1/2
           bg-slate-900 text-white text-sm px-3 py-2 rounded-lg
           opacity-0 pointer-events-none transition-all duration-300
           whitespace-nowrap font-medium;
  }

  .page-dot:hover::after {
    @apply opacity-100 right-10;
  }
}

/* Modern tech grid pattern */
.bg-grid-pattern {
  background-image: linear-gradient(to right, rgba(0, 184, 148, 0.08) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0, 184, 148, 0.08) 1px, transparent 1px);
  background-size: 80px 80px;
  animation: gradient 20s ease infinite;
  background-size: 400% 400%;
}

/* Smooth scroll behavior for better UX */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}