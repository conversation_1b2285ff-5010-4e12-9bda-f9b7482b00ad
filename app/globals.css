@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

:root {
  --foreground-rgb: 30, 41, 59; /* slate-800 from logo text */
  --background-rgb: 255, 255, 255;
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  scroll-snap-type: y mandatory;
  overflow-y: scroll;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Full-page section snap scrolling */
.snap-section {
  scroll-snap-align: start;
  scroll-snap-stop: always;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

@layer components {
  /* Modern primary buttons with electric blue accent */
  .btn-primary {
    @apply bg-gradient-to-r from-accent to-accent-dark
           hover:from-accent-dark hover:to-blue-700
           text-white font-semibold py-4 px-8 rounded-xl
           transition-all duration-300 shadow-lg hover:shadow-xl
           transform hover:-translate-y-0.5 active:translate-y-0;
  }

  /* Secondary buttons with subtle styling */
  .btn-secondary {
    @apply bg-white border-2 border-slate-200
           hover:border-accent hover:bg-accent/5
           text-slate-700 hover:text-accent font-semibold py-4 px-8 rounded-xl
           transition-all duration-300 shadow-sm hover:shadow-md;
  }

  /* Modern navigation links */
  .nav-link {
    @apply text-slate-600 hover:text-accent transition-colors duration-300
           font-medium relative;
  }

  .nav-link::after {
    @apply content-[''] absolute bottom-0 left-0 w-0 h-0.5 bg-accent
           transition-all duration-300;
  }

  .nav-link:hover::after {
    @apply w-full;
  }

  /* Modern cards with enhanced hover effects */
  .card {
    @apply bg-white p-8 rounded-2xl shadow-sm
           border border-slate-100 hover:border-accent/20
           hover:shadow-xl transition-all duration-500
           transform hover:-translate-y-1;
  }

  /* Modern section headings */
  .section-heading {
    @apply text-4xl font-bold text-slate-900 sm:text-5xl lg:text-6xl
           leading-tight tracking-tight;
  }

  /* Enhanced section subheadings */
  .section-subheading {
    @apply text-xl text-slate-600 leading-relaxed sm:text-2xl
           max-w-3xl mx-auto;
  }

  /* Accent elements use electric blue */
  .accent {
    @apply text-accent font-semibold;
  }

  /* Container width for consistent layout */
  .container-width {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Enhanced modern form styles */
  .form-input {
    @apply mt-2 block w-full px-6 py-4
           bg-white border-2 border-slate-200
           rounded-xl shadow-sm
           placeholder:text-slate-400
           focus:outline-none focus:ring-4 focus:ring-accent/10
           focus:border-accent transition-all duration-300
           hover:border-slate-300;
  }

  .form-label {
    @apply block text-sm font-semibold text-slate-700 mb-2;
  }

  /* Form group wrapper for consistent spacing */
  .form-group {
    @apply space-y-2;
  }

  /* Modern feature icon containers */
  .feature-icon {
    @apply w-14 h-14 bg-gradient-to-br from-accent/10 to-accent/5
           rounded-2xl flex items-center justify-center
           transition-all duration-500;
  }

  /* Page indicator styles */
  .page-indicator {
    @apply fixed right-8 top-1/2 transform -translate-y-1/2 z-40
           hidden lg:flex flex-col space-y-3;
  }

  .page-dot {
    @apply w-3 h-3 rounded-full bg-slate-300 cursor-pointer
           transition-all duration-300 hover:bg-accent/50;
  }

  .page-dot.active {
    @apply bg-accent w-4 h-4;
  }
}

/* Modern grid pattern using accent color */
.bg-grid-pattern {
  background-image: linear-gradient(to right, rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
  background-size: 60px 60px;
}

/* Smooth scroll behavior for better UX */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}