@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

:root {
  --foreground-rgb: 30, 41, 59; /* slate-800 from logo text */
  --background-rgb: 255, 255, 255;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
}

@layer components {
  /* Primary buttons use the infinity symbol gradient */
  .btn-primary {
    @apply bg-gradient-to-r from-sky-500 to-blue-600
           hover:from-sky-600 hover:to-blue-700
           text-white font-semibold py-3 px-8 rounded-lg
           transition-all duration-200 shadow-md hover:shadow-lg;
  }

  /* Secondary buttons use the dot gradient */
  .btn-secondary {
    @apply bg-gradient-to-r from-indigo-400 to-indigo-600
           hover:from-indigo-500 hover:to-indigo-700
           text-white font-semibold py-3 px-8 rounded-lg
           transition-all duration-200 shadow-md hover:shadow-lg;
  }

  /* Navigation links use the light blue from infinity symbol */
  .nav-link {
    @apply text-slate-600 hover:text-sky-500 transition-colors duration-200;
  }

  /* Cards with subtle hover effect using logo colors */
  .card {
    @apply bg-white p-6 rounded-xl shadow-sm
           border border-slate-100 hover:border-sky-500/20
           hover:shadow-md transition-all duration-300;
  }

  /* Section headings use logo text color */
  .section-heading {
    @apply text-3xl font-bold text-slate-800 sm:text-4xl;
  }

  /* Section subheadings use a lighter shade */
  .section-subheading {
    @apply text-xl text-slate-600;
  }

  /* Accent elements use the dot color */
  .accent {
    @apply text-indigo-600;
  }

  /* Container width for consistent layout */
  .container-width {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Modern form styles */
  .form-input {
    @apply mt-2 block w-full px-4 py-3
           bg-slate-50 border-0
           rounded-xl shadow-sm ring-1 ring-slate-200
           placeholder:text-slate-400
           focus:outline-none focus:ring-2 focus:ring-sky-500/20 focus:border-0
           transition-all duration-200;
  }

  .form-label {
    @apply block text-sm font-medium text-slate-700 ml-1;
  }

  /* Form group wrapper for consistent spacing */
  .form-group {
    @apply space-y-1;
  }
}

/* Grid pattern using the light blue from infinity symbol */
.bg-grid-pattern {
  background-image: linear-gradient(to right, rgba(14, 165, 233, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(14, 165, 233, 0.1) 1px, transparent 1px);
  background-size: 40px 40px;
}