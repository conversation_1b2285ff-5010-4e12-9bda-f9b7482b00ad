'use client';

export default function Logo({ disableAnimation }: { disableAnimation?: boolean }) {
  return (
    <svg className="h-16 mx-auto" viewBox="0 0 400 160" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="infinityGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#3b82f6" />
          <stop offset="100%" stopColor="#2563eb" />
        </linearGradient>

        <linearGradient id="dotGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#60a5fa" />
          <stop offset="100%" stopColor="#3b82f6" />
        </linearGradient>

        <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="2" result="coloredBlur" />
          <feMerge>
            <feMergeNode in="coloredBlur" />
            <feMergeNode in="SourceGraphic" />
          </feMerge>
        </filter>
      </defs>

      <path
        d="M120,80 C140,80 140,40 160,40 C180,40 180,80 200,80 C220,80 220,40 240,40 C260,40 260,80 280,80"
        fill="none"
        stroke="url(#infinityGradient)"
        strokeWidth="12"
        strokeLinecap="round"
      >
        {!disableAnimation && (
          <animate
            attributeName="stroke-dasharray"
            from="0,1000"
            to="1000,1000"
            dur="2s"
            fill="freeze"
          />
        )}
      </path>

      <circle
        cx="200"
        cy="40"
        r="8"
        fill="url(#dotGradient)"
        filter="url(#glow)"
      >
        {!disableAnimation && (
          <>
            <animate
              attributeName="r"
              values="8;10;8"
              dur="2s"
              repeatCount="indefinite"
            />
            <animate
              attributeName="opacity"
              values="0.7;1;0.7"
              dur="2s"
              repeatCount="indefinite"
            />
          </>
        )}
      </circle>

      <text
        x="200"
        y="130"
        textAnchor="middle"
        fontFamily="Inter, sans-serif"
        fontSize="42"
        fontWeight="400"
        letterSpacing="2"
        fill="#1E293B"
      >
        {!disableAnimation && (
        "difinity"
        )}
        {!disableAnimation && (
          <animate
            attributeName="letter-spacing"
            values="0;2"
            dur="1s"
            fill="freeze"
          />
        )}
      </text>
    </svg>
  );
}