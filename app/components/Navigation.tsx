'use client';

import { useState } from 'react';
import Logo from './Logo';

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setIsOpen(false);
    }
  };

  return (
    <nav className="bg-white/95 backdrop-blur-md shadow-sm fixed w-full z-50 border-b border-slate-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="relative flex justify-center h-20 items-center">
          {/* Center-aligned Logo */}
          <div className="absolute left-1/2 transform -translate-x-1/2">
            <a
              href="#hero"
              onClick={(e) => {
                e.preventDefault();
                scrollToSection('hero');
              }}
              className="flex items-center"
            >
              <Logo />
            </a>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8 absolute right-0">
            <button onClick={() => scrollToSection('features')} className="nav-link">Features</button>
            <button onClick={() => scrollToSection('pricing')} className="nav-link">Pricing</button>
            <button onClick={() => scrollToSection('about')} className="nav-link">About</button>
            <button onClick={() => scrollToSection('contact')} className="nav-link">Contact</button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden absolute right-4">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="inline-flex items-center justify-center p-3 rounded-xl text-slate-600 hover:text-accent hover:bg-accent/5 transition-all duration-300"
            >
              <span className="sr-only">Open main menu</span>
              <i className={`fas ${isOpen ? 'fa-times' : 'fa-bars'} text-lg`}></i>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <div className="md:hidden bg-white/95 backdrop-blur-md border-t border-slate-100">
          <div className="px-4 pt-4 pb-6 space-y-2">
            <button onClick={() => scrollToSection('features')} className="block w-full text-left px-4 py-3 rounded-xl text-slate-600 hover:text-accent hover:bg-accent/5 transition-all duration-300 font-medium">Features</button>
            <button onClick={() => scrollToSection('pricing')} className="block w-full text-left px-4 py-3 rounded-xl text-slate-600 hover:text-accent hover:bg-accent/5 transition-all duration-300 font-medium">Pricing</button>
            <button onClick={() => scrollToSection('about')} className="block w-full text-left px-4 py-3 rounded-xl text-slate-600 hover:text-accent hover:bg-accent/5 transition-all duration-300 font-medium">About</button>
            <button onClick={() => scrollToSection('contact')} className="block w-full text-left px-4 py-3 rounded-xl text-slate-600 hover:text-accent hover:bg-accent/5 transition-all duration-300 font-medium">Contact</button>
          </div>
        </div>
      )}
    </nav>
  );
}