export default function AboutSection() {
  const teamMembers = [
    {
      name: "<PERSON><PERSON>",
      role: "CEO & Founder",
      image: "/team/wasim.jpg",
      description: ""
    },
    {
      name: "<PERSON><PERSON>",
      role: "COO & Co-founder",
      image: "/team/atiq.jpg",
      description: ""
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      role: "CTO & Co-founder",
      image: "/team/shoaib.jpg",
      description: ""
    },
    // ... other team members
  ];

  return (
    <div className="w-full h-full flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-6xl mx-auto w-full">
        <div className="text-center mb-12">
          <h2 className="section-heading">About Difinity.ai</h2>
          <p className="section-subheading mt-6">Revolutionizing enterprise AI governance with complete data sovereignty</p>
        </div>

        <div className="grid md:grid-cols-2 gap-16 items-center">
          <div className="space-y-8" data-aos="fade-right">
            <h3 className="text-3xl font-bold text-slate-900">Our Mission</h3>
            <p className="text-xl text-slate-600 leading-relaxed">
              We solve the fundamental challenge of enterprise AI: maintaining complete control over your data while harnessing the power of AI.
              Unlike traditional solutions that require sending data to external providers, Difinity ensures your data never leaves your organization.
            </p>
            <div className="grid grid-cols-2 gap-6">
              <div className="card text-center">
                <span className="text-accent font-bold text-4xl block mb-2">20-40%</span>
                <p className="text-slate-600 font-semibold">Cost Savings</p>
              </div>
              <div className="card text-center">
                <span className="text-accent font-bold text-4xl block mb-2">100%</span>
                <p className="text-slate-600 font-semibold">Data Sovereignty</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8" data-aos="fade-left">
            {teamMembers.map((member, index) => (
              <div key={index} className="card group text-center">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-32 h-32 object-cover rounded-full mx-auto mb-6 border-4 border-accent/10 group-hover:border-accent/30 transition-all duration-500"
                />
                <h4 className="text-xl font-bold text-slate-900 mb-2">{member.name}</h4>
                <p className="text-accent font-semibold mb-3">{member.role}</p>
                <p className="text-slate-600">{member.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}