export default function AboutSection() {
  const teamMembers = [
    {
      name: "<PERSON><PERSON>",
      role: "CEO & Founder",
      image: "/team/wasim.jpg",
      description: ""
    },
    {
      name: "<PERSON><PERSON>",
      role: "COO & Co-founder",
      image: "/team/atiq.jpg",
      description: ""
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      role: "CTO & Co-founder",
      image: "/team/shoaib.jpg",
      description: ""
    },
    // ... other team members
  ];

  return (
    <div className="container-width flex items-center justify-center min-h-screen py-20">
      <div className="w-full">
        <div className="text-center mb-16">
          <h2 className="section-heading">About Difinity.ai</h2>
          <p className="section-subheading mt-6">Revolutionizing enterprise AI governance with complete data sovereignty</p>
        </div>

        <div className="grid md:grid-cols-2 gap-16 items-center">
          <div className="space-y-8" data-aos="fade-right">
            <h3 className="text-3xl font-bold text-slate-900">Our Mission</h3>
            <p className="text-xl text-slate-600 leading-relaxed">
              We're on a mission to make enterprise AI implementation secure, ethical, and efficient.
              Our platform bridges the gap between powerful AI capabilities and enterprise requirements.
            </p>
            <div className="grid grid-cols-2 gap-6">
              <div className="card text-center">
                <span className="text-accent font-bold text-4xl block mb-2">50+</span>
                <p className="text-slate-600 font-semibold">Enterprise Clients</p>
              </div>
              <div className="card text-center">
                <span className="text-accent font-bold text-4xl block mb-2">1M+</span>
                <p className="text-slate-600 font-semibold">API Calls Daily</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8" data-aos="fade-left">
            {teamMembers.map((member, index) => (
              <div key={index} className="card group text-center">
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-32 h-32 object-cover rounded-full mx-auto mb-6 border-4 border-accent/10 group-hover:border-accent/30 transition-all duration-500"
                />
                <h4 className="text-xl font-bold text-slate-900 mb-2">{member.name}</h4>
                <p className="text-accent font-semibold mb-3">{member.role}</p>
                <p className="text-slate-600">{member.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}