export default function AboutSection() {
  const teamMembers = [
    {
      name: "<PERSON><PERSON>",
      role: "CEO & Founder",
      image: "/team/wasim.jpg",
      description: ""
    },
    {
      name: "<PERSON><PERSON>",
      role: "COO & Co-founder",
      image: "/team/atiq.jpg",
      description: ""
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      role: "CTO & Co-founder",
      image: "/team/shoaib.jpg",
      description: ""
    },
    // ... other team members
  ];

  return (
    <div className="container-width flex items-center justify-center min-h-screen py-20">
      <div className="w-full">
        <div className="text-center mb-16">
          <h2 className="section-heading">About Difinity.ai</h2>
          <p className="section-subheading mt-6">Building the future of AI infrastructure</p>
        </div>

      <div className="mt-16 grid md:grid-cols-2 gap-12">
        <div className="space-y-6" data-aos="fade-right">
          <h3 className="text-2xl font-bold text-slate-800">Our Mission</h3>
          <p className="text-slate-600 leading-relaxed">
            We're on a mission to make enterprise AI implementation secure, ethical, and efficient.
            Our platform bridges the gap between powerful AI capabilities and enterprise requirements.
          </p>
          <div className="flex items-center space-x-4">
            <div className="bg-sky-100 rounded-lg p-4">
              <span className="text-sky-500 font-bold text-2xl">50+</span>
              <p className="text-slate-600 text-sm">Enterprise Clients</p>
            </div>
            <div className="bg-sky-100 rounded-lg p-4">
              <span className="text-sky-500 font-bold text-2xl">1M+</span>
              <p className="text-slate-600 text-sm">API Calls Daily</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-6" data-aos="fade-left">
        {teamMembers.map((member, index) => (
    <div key={index} className="card group">
      <img
        src={member.image}
        alt={member.name}
        className="w-24 h-24 object-cover rounded-full mx-auto mb-4"
      />
      <h4 className="text-lg font-bold text-slate-800 text-center">{member.name}</h4>
      <p className="text-sky-500 font-medium text-sm mb-2 text-center">{member.role}</p>
      <p className="text-slate-600 text-sm text-center">{member.description}</p>
    </div>
  ))}
        </div>
      </div>
    </div>
  );
}