import Logo from "./Logo";
import SocialLinks from "./SocialLinks";

export default function Footer() {
  return (
    <footer className="bg-slate-900 text-white py-12">
      <div className="container-width">
        <div className="grid md:grid-cols-3 gap-8">
          {/* Logo, Description, and Social Links */}
          <div>
            <div className="w-32 mb-0">
              <Logo disableAnimation /> {/* Reduced space between logo and description */}
            </div>
            <p className="text-sm text-slate-400 mb-4">
              Enterprise AI Gateway & Compliance Platform
            </p>

            {/* Social Links */}
            <SocialLinks />
          
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">  
              <li><a href="#features" className="text-slate-400 hover:text-white">Features</a></li>
              <li><a href="#pricing" className="text-slate-400 hover:text-white">Pricing</a></li>
              <li><a href="#about" className="text-slate-400 hover:text-white">About</a></li>
              <li><a href="#contact" className="text-slate-400 hover:text-white">Contact</a></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <ul className="space-y-2">
              <li className="text-slate-400">Email: <EMAIL></li>
              {/* <li className="text-slate-400">Phone: +****************</li> */}
              <li className="text-slate-400">Location: Sydney, NSW</li>
            </ul>
          </div>
        </div>

        <div className="mt-8 text-center text-slate-500 text-sm">
          © {new Date().getFullYear()} Difinity.ai. All rights reserved.
        </div>
      </div>
    </footer>
  );
}