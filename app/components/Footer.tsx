import Logo from "./Logo";
import SocialLinks from "./SocialLinks";

export default function Footer() {
  return (
    <footer className="bg-slate-900 text-white py-16">
      <div className="container-width">
        <div className="grid md:grid-cols-3 gap-12">
          {/* Logo, Description, and Social Links */}
          <div>
            <div className="w-32 mb-0">
              <Logo disableAnimation /> {/* Reduced space between logo and description */}
            </div>
            <p className="text-slate-400 mb-6 leading-relaxed">
              Enterprise AI Gateway & Compliance Platform
            </p>

            {/* Social Links */}
            <SocialLinks />

          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-bold mb-6 text-white">Quick Links</h3>
            <ul className="space-y-3">
              <li><a href="#features" className="text-slate-400 hover:text-accent transition-colors duration-300 font-medium">Features</a></li>
              <li><a href="#pricing" className="text-slate-400 hover:text-accent transition-colors duration-300 font-medium">Pricing</a></li>
              <li><a href="#about" className="text-slate-400 hover:text-accent transition-colors duration-300 font-medium">About</a></li>
              <li><a href="#contact" className="text-slate-400 hover:text-accent transition-colors duration-300 font-medium">Contact</a></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-xl font-bold mb-6 text-white">Contact Us</h3>
            <ul className="space-y-3">
              <li className="text-slate-400 font-medium">Email: <EMAIL></li>
              {/* <li className="text-slate-400">Phone: +****************</li> */}
              <li className="text-slate-400 font-medium">Location: Sydney, NSW</li>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t border-slate-800 text-center text-slate-500">
          © {new Date().getFullYear()} Difinity.ai. All rights reserved.
        </div>
      </div>
    </footer>
  );
}