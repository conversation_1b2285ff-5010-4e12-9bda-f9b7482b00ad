// app/features/page.tsx

export default function FeaturesSection() {
  const mainFeatures = [
    {
      title: "Data Sovereignty",
      description: "Your data never leaves your organization. Complete control over sensitive information with local processing.",
      icon: "shield-alt"
    },
    {
      title: "Unified API",
      description: "Single REST API for OpenAI, Anthropic, Google, Meta, DeepSeek, and Grok with consistent interface.",
      icon: "plug"
    },
    {
      title: "Dynamic Model Selection",
      description: "Intelligent routing that automatically selects optimal models based on query complexity and cost.",
      icon: "random"
    },
    {
      title: "Content Moderation",
      description: "Sophisticated content control with custom policies, keyword filtering, and real-time enforcement.",
      icon: "balance-scale"
    },
    {
      title: "Compliance Automation",
      description: "Built-in support for EU AI Act, GDPR, CCPA, VCDPA with automated compliance verification.",
      icon: "clipboard-list"
    },
    {
      title: "Cost Optimization",
      description: "Real-time cost tracking with 20-40% savings through intelligent routing and usage analytics.",
      icon: "coins"
    },
    {
      title: "Infrastructure Agnostic",
      description: "Deploy on-premises, hybrid cloud, or multi-cloud environments according to your requirements.",
      icon: "server"
    },
    {
      title: "Zero Trust Security",
      description: "End-to-end encryption with authenticated, authorized, and audited access controls.",
      icon: "lock"
    }
  ];

  const platformComponents = [
    {
      title: "Difinity Hub",
      description: "Central management console for user access, API keys, model configuration, content policies, and compliance management.",
      icon: "cogs"
    },
    {
      title: "Difinity Flow",
      description: "Unified AI processing engine with dynamic model selection, content moderation, and automated compliance verification.",
      icon: "stream"
    },
    {
      title: "Difinity Echo",
      description: "Comprehensive monitoring and analytics with real-time usage tracking, cost analysis, and performance metrics.",
      icon: "chart-line"
    }
  ];

  return (
    <div className="container-width">
      <div className="text-center">
        <h2 className="section-heading">Enterprise AI Governance</h2>
        <p className="section-subheading">Complete control, compliance, and cost optimization for your AI workloads</p>
      </div>

      <div className="mt-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {mainFeatures.map((feature, index) => (
          <div
            key={index}
            className="card group"
            data-aos="fade-up"
            data-aos-delay={index * 100}
          >
            <div className="feature-icon mb-6 group-hover:bg-accent/15">
              <i className={`fas fa-${feature.icon} text-accent text-2xl
                           group-hover:text-accent-dark transition-colors duration-500`}></i>
            </div>
            <h3 className="text-xl font-bold mb-4 text-slate-900">{feature.title}</h3>
            <p className="text-slate-600 leading-relaxed">{feature.description}</p>
          </div>
        ))}
      </div>

      {/* Platform Architecture Section */}
      <div className="mt-20">
        <div className="text-center mb-16">
          <h3 className="text-3xl font-bold text-slate-900 mb-4">Platform Architecture</h3>
          <p className="text-xl text-slate-600">Three integrated components for comprehensive AI governance</p>
        </div>

        <div className="grid md:grid-cols-1 lg:grid-cols-3 gap-8">
          {platformComponents.map((component, index) => (
            <div
              key={index}
              className="card group text-center"
              data-aos="fade-up"
              data-aos-delay={index * 150}
            >
              <div className="feature-icon mx-auto mb-6 group-hover:bg-accent/15">
                <i className={`fas fa-${component.icon} text-accent text-xl
                            group-hover:text-accent-dark transition-colors duration-500`}></i>
              </div>
              <h3 className="text-xl font-bold mb-3 text-slate-900">{component.title}</h3>
              <p className="text-slate-600 leading-relaxed">{component.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}