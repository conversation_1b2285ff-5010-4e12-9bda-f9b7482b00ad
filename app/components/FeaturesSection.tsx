// app/features/page.tsx

export default function FeaturesSection() {
  const mainFeatures = [
    {
      title: "Unified API",
      description: "Single API integration for all major AI models",
      icon: "plug"
    },
    {
      title: "Dynamic model selection",
      description: "Intelligently route requests to the most cost-effective and high-performance AI models",
      icon: "check"
    },
    {
      title: "Governance Framework",
      description: "Customizable governance policies for AI operations",
      icon: "shield-alt"
    },
    {
      title: "Built-in Compliance",
      description: "Out-of-the-box compliance with AI regulations",
      icon: "clipboard-list"
    },
    {
      title: "Workflow Monitoring",
      description: "Complete visibility over AI workflows",
      icon: "chart-line"
    },
    {
      title: "Content Moderation",
      description: "Customizable ethical AI implementation",
      icon: "balance-scale"
    },
    {
      title: "Cost Monitoring",
      description: "Track and optimize AI model costs",
      icon: "coins"
    },
    {
      title: "Model Routing",
      description: "Smart routing tailored to specific parameters",
      icon: "random"
    }
  ];

  const additionalFeatures = [
    {
      title: "Audit Logs",
      description: "Comprehensive audit trails for all AI interactions and decisions.",
      icon: "clipboard-list"
    },
    {
      title: "Traceability",
      description: "End-to-end tracking of AI decisions and model interactions.",
      icon: "search"
    },
    {
      title: "Security Controls",
      description: "Enterprise-grade security features to keep your AI operations safe.",
      icon: "lock"
    }
  ];

  return (
    <div className="min-h-screen py-24 bg-gradient-to-b from-slate-50 to-white">
      <div className="container-width">
        <div className="text-center mb-12">
          <h2 className="section-heading">Enterprise Features</h2>
          <p className="section-subheading">Everything you need for AI operations</p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
          {mainFeatures.map((feature, index) => (
            <div 
              key={index} 
              className="card group" 
              data-aos="fade-up" 
              data-aos-delay={index * 50}
            >
              <div className="w-12 h-12 bg-sky-100 rounded-xl flex items-center justify-center mb-4 
                            group-hover:bg-sky-500/10 transition-colors duration-300">
                <i className={`fas fa-${feature.icon} text-sky-500 text-xl 
                             group-hover:text-blue-600 transition-colors duration-300`}></i>
              </div>
              <h3 className="text-lg font-bold mb-2 text-slate-800">{feature.title}</h3>
              <p className="text-sm text-slate-600">{feature.description}</p>
            </div>
          ))}
        </div>

        {/* Additional Features Section */}
        <div className="mt-20">
          <div className="text-center mb-16">
            <h2 className="section-heading">Advanced Capabilities</h2>
            <p className="section-subheading">Powerful features for enterprise needs</p>
          </div>

          <div className="grid md:grid-cols-2 gap-12">
            {additionalFeatures.map((feature, index) => (
              <div 
                key={index} 
                className="card flex items-start space-x-6 group" 
                data-aos={index % 2 === 0 ? "fade-right" : "fade-left"}
              >
                <div className="w-12 h-12 bg-sky-100 rounded-xl flex items-center justify-center flex-shrink-0
                              group-hover:bg-sky-500/10 transition-colors duration-300">
                  <i className={`fas fa-${feature.icon} text-sky-500 text-xl
                              group-hover:text-blue-600 transition-colors duration-300`}></i>
                </div>
                <div>
                  <h3 className="text-lg font-bold mb-2 text-slate-800">{feature.title}</h3>
                  <p className="text-slate-600">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}