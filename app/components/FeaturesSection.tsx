// app/features/page.tsx

export default function FeaturesSection() {
  const mainFeatures = [
    {
      title: "Data Sovereignty",
      description: "Your data never leaves your organization. Complete control over sensitive information with local processing.",
      icon: "shield-alt"
    },
    {
      title: "Unified API",
      description: "Single REST API for OpenAI, Anthropic, Google, Meta, DeepSeek, and Grok with consistent interface.",
      icon: "plug"
    },
    {
      title: "Dynamic Model Selection",
      description: "Intelligent routing that automatically selects optimal models based on query complexity and cost.",
      icon: "random"
    },
    {
      title: "Content Moderation",
      description: "Sophisticated content control with custom policies, keyword filtering, and real-time enforcement.",
      icon: "balance-scale"
    },
    {
      title: "Compliance Automation",
      description: "Built-in support for EU AI Act, GDPR, CCPA, VCDPA with automated compliance verification.",
      icon: "clipboard-list"
    },
    {
      title: "Cost Optimization",
      description: "Real-time cost tracking with 20-40% savings through intelligent routing and usage analytics.",
      icon: "coins"
    },
    {
      title: "Infrastructure Agnostic",
      description: "Deploy on-premises, hybrid cloud, or multi-cloud environments according to your requirements.",
      icon: "server"
    },
    {
      title: "Zero Trust Security",
      description: "End-to-end encryption with authenticated, authorized, and audited access controls.",
      icon: "lock"
    }
  ];

  const platformComponents = [
    {
      title: "Difinity Hub",
      description: "Central management console for user access, API keys, model configuration, content policies, and compliance management.",
      icon: "cogs"
    },
    {
      title: "Difinity Flow",
      description: "Unified AI processing engine with dynamic model selection, content moderation, and automated compliance verification.",
      icon: "stream"
    },
    {
      title: "Difinity Echo",
      description: "Comprehensive monitoring and analytics with real-time usage tracking, cost analysis, and performance metrics.",
      icon: "chart-line"
    }
  ];

  return (
    <div className="container-width flex items-center justify-center min-h-screen py-20">
      <div className="w-full">
        <div className="text-center mb-20">
          <h2 className="section-heading">Enterprise AI Governance</h2>
          <p className="section-subheading mt-6">Complete control, compliance, and cost optimization for your AI workloads</p>
        </div>

        {/* Modern Bento Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-24">
          {mainFeatures.map((feature, index) => (
            <div
              key={index}
              className={`group relative overflow-hidden rounded-3xl bg-gradient-to-br from-white/80 to-white/40
                         backdrop-blur-sm border border-slate-200/50 p-8 transition-all duration-700 hover:scale-105
                         hover:shadow-2xl hover:shadow-accent/20 hover:border-accent/30
                         ${index === 0 || index === 3 ? 'lg:col-span-2' : ''}
                         ${index === 0 ? 'lg:row-span-2' : ''}`}
              data-aos="fade-up"
              data-aos-delay={index * 150}
            >
              {/* Background gradient effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-accent/5 via-tech/5 to-transparent
                            opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

              {/* Content */}
              <div className="relative z-10">
                <div className="feature-icon mb-6 group-hover:from-accent/20 group-hover:to-tech/20 group-hover:scale-110">
                  <i className={`fas fa-${feature.icon} text-accent text-2xl
                               group-hover:text-accent-dark transition-all duration-500`}></i>
                </div>
                <h3 className="text-xl font-bold mb-4 text-slate-900 group-hover:text-slate-800 transition-colors duration-500">
                  {feature.title}
                </h3>
                <p className="text-slate-600 leading-relaxed group-hover:text-slate-700 transition-colors duration-500">
                  {feature.description}
                </p>
              </div>

              {/* Floating elements for visual interest */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-accent/10 to-tech/10
                            rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
            </div>
          ))}
        </div>

        {/* Platform Architecture Section */}
        <div className="mt-32">
          <div className="text-center mb-20">
            <h2 className="text-4xl font-bold text-slate-900 mb-6 bg-gradient-to-r from-slate-900 via-accent to-slate-900 bg-clip-text text-transparent">
              Platform Architecture
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              Three integrated components working in harmony for comprehensive AI governance
            </p>
          </div>

          {/* Modern horizontal layout for platform components */}
          <div className="grid lg:grid-cols-3 gap-8">
            {platformComponents.map((component, index) => (
              <div
                key={index}
                className="group relative"
                data-aos="fade-up"
                data-aos-delay={index * 200}
              >
                {/* Connection lines between components */}
                {index < platformComponents.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-accent to-tech z-10"></div>
                )}

                {/* Component card */}
                <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-white via-white/95 to-accent/5
                              border border-slate-200/50 p-8 h-full transition-all duration-700
                              hover:shadow-2xl hover:shadow-accent/15 hover:-translate-y-2 hover:border-accent/40">

                  {/* Background pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute inset-0 bg-gradient-to-br from-accent/20 to-tech/20"></div>
                  </div>

                  {/* Content */}
                  <div className="relative z-10 text-center">
                    <div className="feature-icon mx-auto mb-6 group-hover:from-accent/25 group-hover:to-tech/25 group-hover:scale-110 group-hover:rotate-3">
                      <i className={`fas fa-${component.icon} text-accent text-2xl
                                  group-hover:text-accent-dark transition-all duration-500`}></i>
                    </div>
                    <h3 className="text-2xl font-bold mb-4 text-slate-900 group-hover:text-accent transition-colors duration-500">
                      {component.title}
                    </h3>
                    <p className="text-slate-600 leading-relaxed group-hover:text-slate-700 transition-colors duration-500">
                      {component.description}
                    </p>
                  </div>

                  {/* Decorative elements */}
                  <div className="absolute -bottom-2 -right-2 w-20 h-20 bg-gradient-to-br from-accent/10 to-tech/10
                                rounded-full blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}