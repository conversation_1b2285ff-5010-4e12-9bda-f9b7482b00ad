// app/features/page.tsx

export default function FeaturesSection() {
  const mainFeatures = [
    {
      title: "Unified API",
      description: "Single API integration for all major AI models",
      icon: "plug"
    },
    {
      title: "Dynamic model selection",
      description: "Intelligently route requests to the most cost-effective and high-performance AI models",
      icon: "check"
    },
    {
      title: "Governance Framework",
      description: "Customizable governance policies for AI operations",
      icon: "shield-alt"
    },
    {
      title: "Built-in Compliance",
      description: "Out-of-the-box compliance with AI regulations",
      icon: "clipboard-list"
    },
    {
      title: "Workflow Monitoring",
      description: "Complete visibility over AI workflows",
      icon: "chart-line"
    },
    {
      title: "Content Moderation",
      description: "Customizable ethical AI implementation",
      icon: "balance-scale"
    },
    {
      title: "Cost Monitoring",
      description: "Track and optimize AI model costs",
      icon: "coins"
    },
    {
      title: "Model Routing",
      description: "Smart routing tailored to specific parameters",
      icon: "random"
    }
  ];

  const additionalFeatures = [
    {
      title: "Audit Logs",
      description: "Comprehensive audit trails for all AI interactions and decisions.",
      icon: "clipboard-list"
    },
    {
      title: "Traceability",
      description: "End-to-end tracking of AI decisions and model interactions.",
      icon: "search"
    },
    {
      title: "Security Controls",
      description: "Enterprise-grade security features to keep your AI operations safe.",
      icon: "lock"
    }
  ];

  return (
    <div className="container-width flex items-center justify-center min-h-screen py-20">
      <div className="w-full">
        <div className="text-center mb-16">
          <h2 className="section-heading">Enterprise Features</h2>
          <p className="section-subheading mt-6">Everything you need for AI operations at scale</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {mainFeatures.map((feature, index) => (
            <div
              key={index}
              className="card group"
              data-aos="fade-up"
              data-aos-delay={index * 100}
            >
              <div className="feature-icon mb-6">
                <i className={`fas fa-${feature.icon} text-accent text-2xl
                             group-hover:text-accent-dark transition-colors duration-500`}></i>
              </div>
              <h3 className="text-xl font-bold mb-4 text-slate-900">{feature.title}</h3>
              <p className="text-slate-600 leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>

        {/* Additional Features Section */}
        <div className="mt-20">
          <div className="text-center mb-16">
            <h2 className="section-heading">Advanced Capabilities</h2>
            <p className="section-subheading">Powerful features for enterprise needs</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {additionalFeatures.map((feature, index) => (
              <div
                key={index}
                className="card flex items-start space-x-6 group"
                data-aos="fade-up"
                data-aos-delay={index * 150}
              >
                <div className="feature-icon flex-shrink-0">
                  <i className={`fas fa-${feature.icon} text-accent text-xl
                              group-hover:text-accent-dark transition-colors duration-500`}></i>
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-3 text-slate-900">{feature.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}