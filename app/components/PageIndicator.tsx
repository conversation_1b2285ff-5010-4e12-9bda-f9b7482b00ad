'use client';

import { useState, useEffect } from 'react';

interface PageIndicatorProps {
  sections: string[];
}

export default function PageIndicator({ sections }: PageIndicatorProps) {
  const [activeSection, setActiveSection] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + window.innerHeight / 2;
      
      sections.forEach((sectionId, index) => {
        const element = document.getElementById(sectionId);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(index);
          }
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial check

    return () => window.removeEventListener('scroll', handleScroll);
  }, [sections]);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  return (
    <div className="page-indicator">
      {sections.map((sectionId, index) => (
        <div
          key={sectionId}
          className={`page-dot ${index === activeSection ? 'active' : ''}`}
          onClick={() => scrollToSection(sectionId)}
          title={sectionId.charAt(0).toUpperCase() + sectionId.slice(1)}
        />
      ))}
    </div>
  );
}
