export default function PricingSection() {
  const features = [
    "Complete data sovereignty - your data never leaves your organization",
    "Unified API for OpenAI, Anthropic, Google, Meta, DeepSeek, and Grok",
    "Automated compliance with EU AI Act, GDPR, CCPA, VCDPA",
    "Real-time content moderation and policy enforcement",
    "20-40% cost savings through intelligent model routing",
    "Infrastructure agnostic deployment (on-premises, hybrid, multi-cloud)",
    "Comprehensive monitoring and analytics with Difinity Echo",
    "Zero trust security with end-to-end encryption"
  ];

  const comparisons = [
    {
      title: "Traditional Integration",
      points: [
        "Multiple API integrations",
        "Manual compliance checks",
        "Basic monitoring",
        "Fixed routing",
        "Limited support",
      ],
      isNegative: true
    },
    {
      title: "With Difinity.ai",
      points: [
        "Single API integration",
        "Automated compliance",
        "Advanced monitoring",
        "Smart routing",
        "24/7 support",
      ],
      isNegative: false
    }
  ];

  return (
    <div className="container-width">
      <div className="text-center">
        <h2 className="section-heading">Enterprise AI Governance</h2>
        <p className="section-subheading">Complete platform with data sovereignty, compliance automation, and cost optimization</p>
      </div>

        <div className="max-w-2xl mx-auto">
          <div className="card text-center" data-aos="fade-up">
            <div className="inline-flex items-center justify-center px-8 py-3 bg-accent/10 rounded-full text-accent font-bold text-lg mb-8">
              Enterprise Deployment
            </div>
            <div className="flex items-center justify-center mb-8">
              <span className="text-5xl font-extrabold text-slate-900">Contact</span>
              <span className="ml-4 text-3xl text-slate-600 font-semibold">Sales</span>
            </div>
            <p className="text-2xl text-slate-600 mb-12 leading-relaxed">
              Custom pricing for private cloud and on-premises deployments
            </p>

            <div className="space-y-6 mb-12">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-4 text-left"
                  data-aos="fade-up"
                  data-aos-delay={index * 100}
                >
                  <svg className="h-6 w-6 text-accent flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-slate-700 text-lg">{feature}</span>
                </div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row sm:space-x-6 items-center justify-center">
              <button className="btn-primary w-64 sm:w-auto mb-4 sm:mb-0">Get Started</button>
              <button className="btn-secondary w-64 sm:w-auto">Contact Sales</button>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
}