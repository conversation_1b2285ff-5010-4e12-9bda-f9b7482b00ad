export default function PricingSection() {
  const features = [
    "Access to all major AI models",
    "Real-time compliance monitoring",
    "Advanced security controls",
    "24/7 priority support",
    "Custom model routing",
    "Usage analytics dashboard",
    "Automated audit logs",
    "Team collaboration tools"
  ];

  const comparisons = [
    {
      title: "Traditional Integration",
      points: [
        "Multiple API integrations",
        "Manual compliance checks",
        "Basic monitoring",
        "Fixed routing",
        "Limited support",
      ],
      isNegative: true
    },
    {
      title: "With Difinity.ai",
      points: [
        "Single API integration",
        "Automated compliance",
        "Advanced monitoring",
        "Smart routing",
        "24/7 support",
      ],
      isNegative: false
    }
  ];

  return (
    
    <div className="min-h-screen py-24 pb-0 bg-gradient-to-b from-slate-50 to-white">
      <div className="container-width">
      <div className="text-center">
        <h2 className="section-heading">Simple, Transparent Pricing</h2>
        <p className="section-subheading">Pay only for what you use, with no hidden fees</p>
      </div>

      <div className="mt-16 bg-white rounded-2xl shadow-lg overflow-hidden" data-aos="fade-up">
        <div className="px-6 py-12 sm:p-16 text-center">
          <div className="inline-flex items-center justify-center px-6 py-2 bg-sky-100 rounded-full text-sky-500 font-semibold text-lg mb-8">
            Usage-Based Pricing
          </div>
          <div className="flex items-center justify-center mb-8">
            <span className="text-6xl font-extrabold text-slate-800">25%</span>
            <span className="ml-3 text-2xl text-slate-500">markup</span>
          </div>
          <p className="text-xl text-slate-600 mb-12">
            on top of your AI model costs
          </p>

          <div className="space-y-4">
            {features.map((feature, index) => (
              <div 
                key={index}
                className="flex items-center space-x-3 text-left"
                data-aos="fade-up"
                data-aos-delay={index * 50}
              >
                <svg className="h-6 w-6 text-sky-500 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-slate-600">{feature}</span>
              </div>
            ))}
          </div>

          <div className="mt-12 flex flex-col sm:flex-row sm:space-x-4 items-center justify-center">
  <button className="btn-primary w-48 sm:w-auto mb-4 sm:mb-0">Get Started</button>
  <button className="btn-primary w-48 sm:w-auto">Contact Sales</button>
</div>
        </div>
      </div>

      {/* Comparison Section */}
      <section className="py-20 bg-gradient-to-b from-white to-indigo-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900">Why Choose Difinity.ai?</h2>
            <p className="mt-4 text-xl text-gray-600">See how we compare to traditional solutions</p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {comparisons.map((comparison, index) => (
              <div 
                key={index}
                className="bg-white p-8 rounded-xl shadow-sm"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                <h3 className="text-xl font-bold mb-6 text-center">{comparison.title}</h3>
                <div className="space-y-4">
                  {comparison.points.map((point, pointIndex) => (
                    <div key={pointIndex} className="flex items-center space-x-3">
                      <i className={`fas ${comparison.isNegative ? 'fa-times text-red-500' : 'fa-check text-green-500'}`}></i>
                      <span className="text-gray-600">{point}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 pb-0">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900">Frequently Asked Questions</h2>
            <p className="mt-4 text-xl text-gray-600">Everything you need to know about our pricing</p>
          </div>

          <div className="space-y-8">
            {[
              {
                q: "How does usage-based pricing work?",
                a: "We charge a 25% markup on your AI model costs. This means if you spend $100 on API calls, our fee would be $25."
              },
              {
                q: "Is there a minimum commitment?",
                a: "No, there's no minimum commitment. You only pay for what you use."
              },
              {
                q: "Do you offer volume discounts?",
                a: "Yes, we offer custom pricing for enterprise customers with high volume needs."
              }
            ].map((faq, index) => (
              <div 
                key={index}
                className="bg-white p-6 rounded-lg shadow-sm"
                data-aos="fade-up"
                data-aos-delay={index * 100}
              >
                <h3 className="text-lg font-bold text-gray-900 mb-2">{faq.q}</h3>
                <p className="text-gray-600">{faq.a}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
    
    </div>
  );
}