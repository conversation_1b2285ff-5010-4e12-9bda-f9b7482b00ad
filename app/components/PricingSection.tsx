export default function PricingSection() {
  const features = [
    "Access to all major AI models",
    "Real-time compliance monitoring",
    "Advanced security controls",
    "24/7 priority support",
    "Custom model routing",
    "Usage analytics dashboard",
    "Automated audit logs",
    "Team collaboration tools"
  ];

  const comparisons = [
    {
      title: "Traditional Integration",
      points: [
        "Multiple API integrations",
        "Manual compliance checks",
        "Basic monitoring",
        "Fixed routing",
        "Limited support",
      ],
      isNegative: true
    },
    {
      title: "With Difinity.ai",
      points: [
        "Single API integration",
        "Automated compliance",
        "Advanced monitoring",
        "Smart routing",
        "24/7 support",
      ],
      isNegative: false
    }
  ];

  return (
    <div className="container-width flex items-center justify-center min-h-screen py-20">
      <div className="w-full">
        <div className="text-center mb-16">
          <h2 className="section-heading">Simple, Transparent Pricing</h2>
          <p className="section-subheading mt-6">Pay only for what you use, with no hidden fees</p>
        </div>

        <div className="max-w-2xl mx-auto">
          <div className="card text-center" data-aos="fade-up">
            <div className="inline-flex items-center justify-center px-8 py-3 bg-accent/10 rounded-full text-accent font-bold text-lg mb-8">
              Usage-Based Pricing
            </div>
            <div className="flex items-center justify-center mb-8">
              <span className="text-7xl font-extrabold text-slate-900">10%</span>
              <span className="ml-4 text-3xl text-slate-600 font-semibold">markup</span>
            </div>
            <p className="text-2xl text-slate-600 mb-12 leading-relaxed">
              on top of your AI model costs
            </p>

            <div className="space-y-6 mb-12">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-4 text-left"
                  data-aos="fade-up"
                  data-aos-delay={index * 100}
                >
                  <svg className="h-6 w-6 text-accent flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-slate-700 text-lg">{feature}</span>
                </div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row sm:space-x-6 items-center justify-center">
              <button className="btn-primary w-64 sm:w-auto mb-4 sm:mb-0">Get Started</button>
              <button className="btn-secondary w-64 sm:w-auto">Contact Sales</button>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
}