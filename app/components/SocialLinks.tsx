export default function SocialLinks() {
  const socialLinks = [
    {
      name: "Twitter",
      url: "https://twitter.com",
      imgSrc: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/twitter/twitter-original.svg",
    },
    {
      name: "Facebook",
      url: "https://facebook.com",
      imgSrc: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/facebook/facebook-original.svg",
    },
    {
      name: "LinkedIn",
      url: "https://linkedin.com",
      imgSrc: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/linkedin/linkedin-original.svg",
    },
    {
      name: "Instagram",
      url: "https://instagram.com",
      imgSrc: "https://upload.wikimedia.org/wikipedia/commons/e/e7/Instagram_logo_2016.svg",
    },
  ];


    return (
      <div className="flex justify-start space-x-4">
        {socialLinks.map((link) => (
          <a
            key={link.name}
            href={link.url}
            target="_blank"
            rel="noopener noreferrer"
            aria-label={link.name}
          >
            <img src={link.imgSrc} alt={link.name} className="w-6 h-6" />
          </a>
        ))}
      </div>
    );
  }