export default function ContactSection() {
  return (
    <div className="container-width">
      <div className="text-center">
        <h2 className="section-heading">Ready to Transform Your AI Governance?</h2>
        <p className="section-subheading">Discover how Difinity can provide complete data sovereignty while optimizing your AI operations</p>
      </div>

      <div className="mt-16 max-w-2xl mx-auto">
        <form className="card space-y-8" data-aos="fade-up">
            <div className="form-group">
              <label htmlFor="name" className="form-label">Name</label>
              <input
                type="text"
                id="name"
                className="form-input"
                placeholder="Your full name"
              />
            </div>

            <div className="form-group">
              <label htmlFor="email" className="form-label">Email</label>
              <input
                type="email"
                id="email"
                className="form-input"
                placeholder="<EMAIL>"
              />
            </div>

            <div className="form-group">
              <label htmlFor="message" className="form-label">Message</label>
              <textarea
                id="message"
                rows={6}
                className="form-input resize-none"
                placeholder="Tell us about your AI governance requirements, compliance needs, and deployment preferences..."
              ></textarea>
            </div>

            <div className="pt-4">
              <button type="submit" className="btn-primary w-full text-lg py-5">
                Send Message
              </button>
            </div>
          </form>

        </div>
      </div>
    </div>
  );
}