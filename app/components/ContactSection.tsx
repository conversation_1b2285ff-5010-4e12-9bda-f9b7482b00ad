export default function ContactSection() {
  return (
    <div className="container-width">
      <div className="text-center">
        <h2 className="section-heading">Get in Touch</h2>
        <p className="section-subheading">We'd love to hear from you</p>
      </div>

      <div className="mt-16 max-w-2xl mx-auto">
        <form className="space-y-8 bg-white p-8 rounded-2xl shadow-sm">
          <div className="form-group relative">
            <input
              type="text"
              id="name"
              className="form-input peer"
              placeholder=" "
            />
            <label
              htmlFor="name"
              className="form-label absolute left-3 top-3 text-gray-500 transition-all peer-placeholder-shown:top-3 peer-placeholder-shown:text-gray-500 peer-placeholder-shown:text-base peer-focus:top-0 peer-focus:text-sm peer-focus:text-sky-500"
            >
              Name
            </label>
          </div>

          <div className="form-group relative">
            <input
              type="email"
              id="email"
              className="form-input peer"
              placeholder=" "
            />
            <label
              htmlFor="email"
              className="form-label absolute left-3 top-3 text-gray-500 transition-all peer-placeholder-shown:top-3 peer-placeholder-shown:text-gray-500 peer-placeholder-shown:text-base peer-focus:top-0 peer-focus:text-sm peer-focus:text-sky-500"
            >
              Email
            </label>
          </div>

          <div className="form-group relative">
            <textarea
              id="message"
              rows={4}
              className="form-input peer resize-none"
              placeholder=" "
            ></textarea>
            <label
              htmlFor="message"
              className="form-label absolute left-3 top-3 text-gray-500 transition-all peer-placeholder-shown:top-3 peer-placeholder-shown:text-gray-500 peer-placeholder-shown:text-base peer-focus:top-0 peer-focus:text-sm peer-focus:text-sky-500"
            >
              Message
            </label>
          </div>

          <div className="pt-4">
            <button type="submit" className="btn-primary w-full">
              Send Message
            </button>
          </div>
        </form>
      
        {/*
         * Commented out until we confirm the following: Email, Phone, Location 
         */}
        {/* <div className="mt-16 grid md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-12 h-12 bg-sky-50 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:bg-sky-100 transition-colors duration-300">
              <i className="fas fa-envelope text-sky-500"></i>
            </div>
            <h3 className="text-lg font-semibold text-slate-800 mb-2">Email</h3>
            <p className="text-slate-600"><EMAIL></p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-sky-50 rounded-xl flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-phone text-sky-500"></i>
            </div>
            <h3 className="text-lg font-semibold text-slate-800 mb-2">Phone</h3>
            <p className="text-slate-600">+****************</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-sky-50 rounded-xl flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-map-marker-alt text-sky-500"></i>
            </div>
            <h3 className="text-lg font-semibold text-slate-800 mb-2">Location</h3>
            <p className="text-slate-600">Sydney, NSW</p>
          </div>
        </div> */}
      </div>
    </div>
  );
}