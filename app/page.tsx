import Navigation from './components/Navigation';
import Footer from './components/Footer';
import FeaturesSection from './components/FeaturesSection';
import PricingSection from './components/PricingSection';
import AboutSection from './components/AboutSection';
import ContactSection from './components/ContactSection';
import PageIndicator from './components/PageIndicator';

export default function Home() {
  const sections = ['hero', 'features', 'pricing', 'about', 'contact'];

  return (
    <main className="min-h-screen overflow-x-hidden">
      <PageIndicator sections={sections} />

      {/* Hero Section */}
      <section id="hero" className="snap-section bg-gradient-to-br from-slate-50 via-white to-accent/5 relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-30"></div>
        <div className="container-width relative z-10 flex items-center justify-center min-h-screen">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="section-heading animate-slide-up">
              <span className="block text-slate-900">Enterprise AI</span>
              <span className="block text-accent mt-2">Governance Platform</span>
            </h1>
            <p className="section-subheading mt-8 animate-slide-up" style={{animationDelay: '0.2s'}}>
              Complete AI governance with data sovereignty. Your data stays within your organization while you harness the power of AI with full control, compliance, and cost optimization.
            </p>
            <div className="mt-12 flex flex-col sm:flex-row sm:space-x-6 items-center justify-center animate-slide-up" style={{animationDelay: '0.4s'}}>
              <button className="btn-primary w-64 sm:w-auto mb-4 sm:mb-0">Get Started</button>
              <button className="btn-secondary w-64 sm:w-auto">Learn More</button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="snap-section bg-white">
        <FeaturesSection />
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="snap-section bg-gradient-to-br from-slate-50 to-white">
        <PricingSection />
      </section>

      {/* About Section */}
      <section id="about" className="snap-section bg-white">
        <AboutSection />
      </section>

      {/* Contact Section */}
      <section id="contact" className="snap-section bg-gradient-to-br from-slate-50 to-accent/5">
        <ContactSection />
      </section>

      {/* Footer */}
      <Footer />
    </main>
  );
}