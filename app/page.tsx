import Navigation from './components/Navigation';
import Footer from './components/Footer';
import FeaturesSection from './components/FeaturesSection';
import PricingSection from './components/PricingSection';
import AboutSection from './components/AboutSection';
import ContactSection from './components/ContactSection';

export default function Home() {
  return (
    <main className="min-h-screen overflow-x-hidden">
      {/* Hero Section */}
      <section id="hero" className="bg-gradient-to-b from-sky-50 to-white pt-32 pb-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="container-width relative">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="section-heading">
              <span className="block">Enterprise-Grade</span>
              <span className="block text-sky-500">AI Infrastructure</span>
            </h1>
            <p className="section-subheading mt-6">
              Everything you need to build, deploy, and manage AI applications at scale
            </p>
            <div className="mt-8 flex flex-col sm:flex-row sm:space-x-4 items-center justify-center">
              <button className="btn-primary w-48 sm:w-auto mb-4 sm:mb-0">Get Started</button>
              <button className="btn-primary w-48 sm:w-auto">Learn More</button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 pb-0 pt-0">
        <FeaturesSection />
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-16 pt-0 pb-0 bg-gradient-to-b from-white to-indigo-50">
        <PricingSection />
      </section>

      {/* About Section */}
      <section id="about" className="py-24">
        <AboutSection />
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-24 bg-gradient-to-b from-white to-sky-50">
        <ContactSection />
      </section>

      {/* Footer */}
      <Footer />
    </main>
  );
}